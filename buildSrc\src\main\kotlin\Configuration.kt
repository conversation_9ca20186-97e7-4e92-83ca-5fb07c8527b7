object ProjectConfig {
    const val VERSION_CODE = 52
    const val VERSION_NAME = "2.0.1"
}

object AndroidConfig {
    const val COMPILE_SDK_VERSION = 35
    const val MIN_SDK_VERSION = 21
    const val TARGET_SDK_VERSION = 35
    const val BUILD_TOOLS_VERSION = "35.0.0"
    const val NDK_VERSION = "26.3.11579264"
}

object LibsVersion {
    const val KOTLIN = "2.0.0"
    const val VERSIONS = "0.51.0"
    const val DETEKT = "1.23.6"
    const val KT_LINT_GRADLE = "12.1.1"
    const val NAV = "2.7.7"
    const val LIFECYCLE = "2.8.1"
    const val ROOM = "2.6.1"
    const val COROUTINES = "1.8.1"
    const val KOIN = "3.5.6"
    const val COIL = "1.4.0"
    const val OK_HTTP = "4.12.0"
    const val ESPRESSO = "3.4.0"
    const val MATERIAL_DIALOG = "3.3.0"
}

object Libs {
    const val ANDROID_GRADLE_PLUGIN = "com.android.tools.build:gradle:8.11.0"
    const val JDK_DESUGAR = "com.android.tools:desugar_jdk_libs:2.0.4"
    const val FIREBASE_CRASHLYTICS = "com.google.firebase:firebase-crashlytics-gradle:3.0.1"
    const val GOOGLE_SERVICE = "com.google.gms:google-services:4.4.1"
    const val LINT = "com.android.tools.lint:lint:31.4.1"

    object AndroidX {
        const val APP_COMPAT = "androidx.appcompat:appcompat:1.7.0"
        const val CORE = "androidx.core:core-ktx:1.13.1"
        const val CONSTRAINT_LAYOUT = "androidx.constraintlayout:constraintlayout:2.1.4"
        const val SWIPE_REFRESH = "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
        const val VIEW_PAGER2 = "androidx.viewpager2:viewpager2:1.1.0"
        const val PAGING = "androidx.paging:paging-runtime-ktx:3.3.0"
        const val DATA_STORE = "androidx.datastore:datastore-preferences:1.1.1"
        const val WORK_MANAGER = "androidx.work:work-runtime-ktx:2.9.0"

        object KTX {
            const val FRAGMENT = "androidx.fragment:fragment-ktx:1.7.1"
            const val ACTIVITY = "androidx.activity:activity-ktx:1.9.0"
            const val PREFERENCES = "androidx.preference:preference-ktx:1.2.1"
            const val ROOM = "androidx.room:room-ktx:${LibsVersion.ROOM}"
        }

        object Lifecycle {
            const val VIEW_MODEL =
                "androidx.lifecycle:lifecycle-viewmodel-ktx:${LibsVersion.LIFECYCLE}"
            const val LIVE_DATA =
                "androidx.lifecycle:lifecycle-livedata-ktx:${LibsVersion.LIFECYCLE}"
            const val RUNTIME = "androidx.lifecycle:lifecycle-runtime-ktx:${LibsVersion.LIFECYCLE}"
            const val COMPILER = "androidx.lifecycle:lifecycle-compiler:${LibsVersion.LIFECYCLE}"
        }

        object Navigation {
            const val NAV_FRAGMENT = "androidx.navigation:navigation-fragment-ktx:${LibsVersion.NAV}"
            const val NAV_UI = "androidx.navigation:navigation-ui-ktx:${LibsVersion.NAV}"
            const val SAFE_ARGS_GRADLE_PLUGIN =
                "androidx.navigation:navigation-safe-args-gradle-plugin:${LibsVersion.NAV}"
        }

        object Room {
            const val RUNTIME = "androidx.room:room-runtime:${LibsVersion.ROOM}"
            const val COMPILER = "androidx.room:room-compiler:${LibsVersion.ROOM}"
        }
    }

    object Google {
        const val MATERIAL = "com.google.android.material:material:1.12.0"

        object Firebase {
            const val BOM = "com.google.firebase:firebase-bom:33.1.0"
            const val CRASHLYTICS = "com.google.firebase:firebase-crashlytics-ktx"
            const val ANALYTICS = "com.google.firebase:firebase-analytics-ktx"
            // Messaging
            const val MESSAGING = "com.google.firebase:firebase-messaging-ktx"
        }
    }

    object Kotlin {
        object Coroutines {
            const val CORE =
                "org.jetbrains.kotlinx:kotlinx-coroutines-core:${LibsVersion.COROUTINES}"
            const val ANDROID =
                "org.jetbrains.kotlinx:kotlinx-coroutines-android:${LibsVersion.COROUTINES}"
        }
    }

    object Koin {
        const val CORE = "io.insert-koin:koin-android:${LibsVersion.KOIN}"
    }

    object CoilKt {
        const val CORE = "io.coil-kt:coil:${LibsVersion.COIL}"
    }

    object Square {
        const val RETROFIT = "com.squareup.retrofit2:retrofit:2.11.0"
        const val OKHTTP_LOGGING = "com.squareup.okhttp3:logging-interceptor:${LibsVersion.OK_HTTP}"
    }

    object Network {
        const val RETROFIT_KOTLINX_SERIALIZATION_CONVERTER =
            "com.jakewharton.retrofit:retrofit2-kotlinx-serialization-converter:1.0.0"
        const val KOTLINX_SERIALIZATION = "org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3"
    }

    object Testing {
        const val JUNIT = "junit:junit:4.13.2"
        const val JUNIT_EXT = "androidx.test.ext:junit:1.1.3"
        object Espresso {
            const val CORE = "androidx.test.espresso:espresso-core:${LibsVersion.ESPRESSO}"
        }
    }

    object MaterialDialog {
        const val CORE = "com.afollestad.material-dialogs:core:${LibsVersion.MATERIAL_DIALOG}"
    }

    // other library
    const val TIMBER = "com.jakewharton.timber:timber:5.0.1"
    const val OTP_VIEW = "com.github.mukeshsolanki:android-otpview-pinview:2.1.2"
    const val MUPDF = "com.artifex.mupdf:viewer:1.15.0"
    const val SHIMMER = "com.facebook.shimmer:shimmer:0.5.0"
    const val COMPRESSOR = "id.zelory:compressor:3.0.1"
}
