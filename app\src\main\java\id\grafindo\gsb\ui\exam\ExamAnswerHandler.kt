package id.grafindo.gsb.ui.exam

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.Context
import android.graphics.Color
import android.view.DragEvent
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.CompoundButton
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.children
import id.grafindo.gsb.R
import id.grafindo.gsb.data.remote.json.TipeSoal
import id.grafindo.gsb.databinding.FragmentExamQuestionBinding
import id.grafindo.gsb.domain.model.ExamNumber
import id.grafindo.gsb.domain.model.ExamQuestion
import timber.log.Timber

class ExamAnswerHandler(
    private val context: Context,
    private val layoutExam: FragmentExamQuestionBinding,
    private val questionRenderer: ExamQuestionRenderer,
    private val onAnswerChanged: (ExamNumber) -> Unit
) {
    private val selectedOptionMappings =
        mutableMapOf<Long, MutableList<Long>>() // Map<DropLabelId, List<OptionId>>

    private var selectedContainer: LinearLayout? = null
    private var selectedRadioButton: RadioButton? = null

    fun setupAnswerOptions(data: ExamQuestion) {
        questionRenderer.setupAnswerOptions(data)
    }

    fun updateActiveNumber(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        android.util.Log.d("ExamAnswerHandler", "updateActiveNumber called - examNumber: soalId=${examNumber.soalId}, nomorSoal=${examNumber.nomorSoal}, currentQuestion: id=${currentQuestion.id}, tipeSoal=${currentQuestion.tipeSoal}")

        // Validate that examNumber matches currentQuestion
        if (examNumber.soalId != currentQuestion.id) {
            android.util.Log.w("ExamAnswerHandler", "Mismatch! examNumber.soalId=${examNumber.soalId} != currentQuestion.id=${currentQuestion.id}. Skipping setup.")
            return
        }

        when (currentQuestion.tipeSoal) {
            TipeSoal.PilihanGanda -> setupSingleChoiceAnswer(examNumber, currentQuestion)
            TipeSoal.PilihanGandaKompleks -> setupMultipleChoiceAnswer(examNumber, currentQuestion)
            else -> setupMatchingAnswer(examNumber, currentQuestion)
        }
        setupDoubtButton(examNumber)

        android.util.Log.d("ExamAnswerHandler", "updateActiveNumber completed - answer handlers setup for ${currentQuestion.tipeSoal}")
    }

    private fun setupSingleChoiceAnswer(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        android.util.Log.d("ExamAnswerHandler", "setupSingleChoiceAnswer started")

        // Disable RadioGroup's default behavior to prevent conflicts
        layoutExam.radioGroupChoices.setOnCheckedChangeListener(null)
        layoutExam.radioGroupChoices.clearCheck()

        // Collect all RadioButtons and their containers
        val radioButtonData = mutableListOf<Pair<RadioButton, ViewGroup?>>()

        layoutExam.radioGroupChoices.apply {
            for (i in 0 until childCount) {
                val view = getChildAt(i)
                val radioButton = when (view) {
                    is RadioButton -> view
                    is ViewGroup -> view.children.find { it is RadioButton } as? RadioButton
                    else -> null
                }
                radioButton?.let {
                    radioButtonData.add(Pair(it, view as? ViewGroup))
                }
            }
        }

        android.util.Log.d("ExamAnswerHandler", "Found ${radioButtonData.size} radio buttons")

        // Clear all existing listeners and states
        radioButtonData.forEach { (rb, _) ->
            rb.setOnCheckedChangeListener(null)
            rb.setOnClickListener(null)
            rb.isChecked = false
        }

        // Set up click listeners on both containers and radio buttons
        radioButtonData.forEachIndexed { index, (radioButton, container) ->
            android.util.Log.d("ExamAnswerHandler", "Setting up click listener for radio button $index")

            val clickHandler = {
                android.util.Log.d("ExamAnswerHandler", "Radio button $index clicked!")

                // Check if this radio button is already the only one checked
                val currentlyCheckedButtons = radioButtonData.filter { it.first.isChecked }
                val isOnlyChecked = currentlyCheckedButtons.size == 1 && currentlyCheckedButtons[0].first == radioButton

                if (!isOnlyChecked) {
                    // Uncheck all radio buttons first
                    radioButtonData.forEach { (otherRb, _) ->
                        otherRb.isChecked = false
                    }

                    // Check the selected radio button
                    radioButton.isChecked = true

                    // Handle the selection
                    if (index < currentQuestion.dataJawaban.size) {
                        val option = currentQuestion.dataJawaban[index].id.toString()
                        val newAnswer = examNumber.copy(pilihan = option)

                        // Debug logging
                        android.util.Log.d("ExamAnswerHandler", "Single choice - Original examNumber: soalId=${examNumber.soalId}, nomorSoal=${examNumber.nomorSoal}")
                        android.util.Log.d("ExamAnswerHandler", "Single choice - New answer: soalId=${newAnswer.soalId}, nomorSoal=${newAnswer.nomorSoal}, pilihan=${newAnswer.pilihan}")

                        onAnswerChanged(newAnswer)
                        updateSelectionState(radioButton)
                    }
                } else {
                    android.util.Log.d("ExamAnswerHandler", "Radio button $index is already the only selected option, ignoring click")
                }
            }

            // Set click listener on both the container and the radio button itself
            container?.setOnClickListener { clickHandler() }
            radioButton.setOnClickListener { clickHandler() }
        }

        // Set up previous choice
        setupPreviousChoice(examNumber, currentQuestion)

        android.util.Log.d("ExamAnswerHandler", "setupSingleChoiceAnswer completed")
    }

    private fun setupMultipleChoiceAnswer(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        android.util.Log.d("ExamAnswerHandler", "setupMultipleChoiceAnswer started")

        layoutExam.checkboxGroupChoices.apply {
            val checkBoxes = mutableListOf<CheckBox>()

            // Collect all actual CheckBoxes (whether directly or inside containers)
            for (i in 0 until childCount) {
                when (val child = getChildAt(i)) {
                    is CheckBox -> checkBoxes.add(child)
                    is ViewGroup -> {
                        // Traverse children to find CheckBox
                        for (j in 0 until child.childCount) {
                            val nested = child.getChildAt(j)
                            if (nested is CheckBox) {
                                checkBoxes.add(nested)
                                break // Assume only one CheckBox per container
                            }
                        }
                    }
                }
            }

            android.util.Log.d("ExamAnswerHandler", "Found ${checkBoxes.size} checkboxes")

            // Now apply listeners to the collected CheckBoxes
            checkBoxes.forEachIndexed { index, checkBox ->
                android.util.Log.d("ExamAnswerHandler", "Setting up change listener for checkbox $index")

                checkBox.setOnCheckedChangeListener(null)
                checkBox.setOnCheckedChangeListener { _, isChecked ->
                    android.util.Log.d("ExamAnswerHandler", "Checkbox $index changed to: $isChecked")

                    if (index in currentQuestion.dataJawaban.indices) {
                        val option = currentQuestion.dataJawaban[index].id.toString()

                        // Get current state from ALL checkboxes, not from examNumber.pilihan
                        val currentChoices = mutableListOf<String>()
                        checkBoxes.forEachIndexed { cbIndex, cb ->
                            if (cb.isChecked && cbIndex in currentQuestion.dataJawaban.indices) {
                                currentChoices.add(currentQuestion.dataJawaban[cbIndex].id.toString())
                            }
                        }

                        val newAnswer = examNumber.copy(pilihan = currentChoices.joinToString(","))

                        // Debug logging
                        android.util.Log.d("ExamAnswerHandler", "Multiple choice - Original examNumber: soalId=${examNumber.soalId}, nomorSoal=${examNumber.nomorSoal}")
                        android.util.Log.d("ExamAnswerHandler", "Multiple choice - New answer: soalId=${newAnswer.soalId}, nomorSoal=${newAnswer.nomorSoal}, pilihan=${newAnswer.pilihan}")

                        onAnswerChanged(newAnswer)
                    }
                }
            }

            // Set up previous choice AFTER setting up listeners
            setupPreviousChoice(examNumber, currentQuestion)
        }

        android.util.Log.d("ExamAnswerHandler", "setupMultipleChoiceAnswer completed")
    }

    private fun setupMatchingAnswer(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        android.util.Log.d("ExamAnswerHandler", "setupMatchingAnswer started")

        selectedOptionMappings.clear()

        // Set up drag listeners for drop targets
        layoutExam.optionsContainer.apply {
            android.util.Log.d("ExamAnswerHandler", "Setting up drop targets, count: $childCount")

            for (index in 0 until childCount) {
                val dropLayout = getChildAt(index) as LinearLayout
                val targetView = dropLayout.getChildAt(2) as TextView

                android.util.Log.d("ExamAnswerHandler", "Setting up drop target $index")

                targetView.setOnDragListener { view, event ->
                    handleDragEvent(view, event, index, examNumber, currentQuestion)
                }
                targetView.setOnClickListener {
                    android.util.Log.d("ExamAnswerHandler", "Drop target $index clicked")
                    onTargetViewClick(dropLayout, targetView, examNumber)
                }
            }
        }

        // Set up drag listeners for option buttons
        layoutExam.targetsContainer.apply {
            android.util.Log.d("ExamAnswerHandler", "Setting up option buttons, count: $childCount")

            for (index in 0 until childCount) {
                val optionLayout = getChildAt(index) as LinearLayout
                val button = optionLayout.getChildAt(0)
                val label = 'A' + index

                android.util.Log.d("ExamAnswerHandler", "Setting up option button $index ($label), button type: ${button::class.simpleName}")

                if (button is android.widget.Button) {
                    button.setOnLongClickListener { view ->
                        android.util.Log.d("ExamAnswerHandler", "Option button $index ($label) long clicked - starting drag")
                        startDrag(view, label)
                        true
                    }
                } else {
                    android.util.Log.e("ExamAnswerHandler", "Expected Button but got ${button::class.simpleName} at index $index")
                }
            }
        }

        // Set up previous choice AFTER setting up listeners
        setupPreviousChoice(examNumber, currentQuestion)

        android.util.Log.d("ExamAnswerHandler", "setupMatchingAnswer completed")
    }

    private fun setupPreviousChoice(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        when (currentQuestion.tipeSoal) {
            TipeSoal.PilihanGanda -> setupSingleChoice(examNumber, currentQuestion)
            TipeSoal.PilihanGandaKompleks -> setupMultipleChoice(examNumber, currentQuestion)
            else -> setupMatchChoice(examNumber)
        }
    }

    private fun setupSingleChoice(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        if (examNumber.pilihan.isEmpty()) return

        android.util.Log.d("ExamAnswerHandler", "setupSingleChoice - restoring pilihan: ${examNumber.pilihan}")

        layoutExam.radioGroupChoices.apply {
            var answerIndex = 0

            for (index in 0 until childCount) {
                val view = getChildAt(index)
                val radioButton = when (view) {
                    is RadioButton -> view
                    is ViewGroup -> view.children.find { it is RadioButton } as? RadioButton
                    else -> null
                }

                radioButton?.let {
                    if (answerIndex < currentQuestion.dataJawaban.size) {
                        val option = currentQuestion.dataJawaban[answerIndex].id.toString()
                        if (examNumber.pilihan == option) {
                            // Set isChecked AND visual state
                            it.isChecked = true
                            updateSelectionState(it)
                            android.util.Log.d("ExamAnswerHandler", "Restored selection for radio button $answerIndex (option: $option)")
                        }
                        answerIndex++
                    }
                }
            }
        }
    }

    private fun setupMultipleChoice(examNumber: ExamNumber, currentQuestion: ExamQuestion) {
        val selectedIds = examNumber.pilihan.split(",").filter { it.isNotEmpty() }

        android.util.Log.d("ExamAnswerHandler", "setupMultipleChoice - restoring pilihan: ${examNumber.pilihan}, selectedIds: $selectedIds")

        layoutExam.checkboxGroupChoices.apply {
            val checkBoxes = mutableListOf<CheckBox>()

            for (i in 0 until childCount) {
                when (val child = getChildAt(i)) {
                    is CheckBox -> checkBoxes.add(child)
                    is ViewGroup -> {
                        for (j in 0 until child.childCount) {
                            val nested = child.getChildAt(j)
                            if (nested is CheckBox) {
                                checkBoxes.add(nested)
                                break
                            }
                        }
                    }
                }
            }

            checkBoxes.forEachIndexed { index, checkBox ->
                val answerId = currentQuestion.dataJawaban.getOrNull(index)?.id?.toString()
                val shouldBeChecked = selectedIds.contains(answerId)
                checkBox.isChecked = shouldBeChecked

                if (shouldBeChecked) {
                    android.util.Log.d("ExamAnswerHandler", "Restored selection for checkbox $index (answerId: $answerId)")
                }
            }
        }
    }

    private fun setupMatchChoice(examNumber: ExamNumber) {
        if (examNumber.pilihan.isNotEmpty()) {
            examNumber.pilihan.split("],[").forEach { item ->
                val (dropLabelId, optionId) = item.removePrefix("[").removeSuffix("]")
                    .split(",")
                val dropLabelIdInt = dropLabelId.toLong()
                val optionIdInt = optionId.toLong()

                // Add the option to the selectedOptionMappings for this dropLabelId
                if (!selectedOptionMappings.containsKey(dropLabelIdInt)) {
                    selectedOptionMappings[dropLabelIdInt] = mutableListOf()
                }
                selectedOptionMappings[dropLabelIdInt]?.add(optionIdInt)
            }

            // After populating the selectedOptionMappings, update the UI
            updateUIWithSelections()
        }
    }

    private fun setupDoubtButton(examNumber: ExamNumber) {
        layoutExam.btnDoubt.apply {
            isChecked = examNumber.ragu
            setOnCheckedChangeListener { _, isChecked ->
                setBackgroundResource(if (isChecked) R.drawable.btn_doubt_alt else R.drawable.btn_doubt)
            }
            setOnClickListener {
                val newAnswer = examNumber.copy(ragu = isChecked)

                // Debug logging
                android.util.Log.d("ExamAnswerHandler", "Doubt button - Original examNumber: soalId=${examNumber.soalId}, nomorSoal=${examNumber.nomorSoal}")
                android.util.Log.d("ExamAnswerHandler", "Doubt button - New answer: soalId=${newAnswer.soalId}, nomorSoal=${newAnswer.nomorSoal}, ragu=${newAnswer.ragu}")

                onAnswerChanged(newAnswer)
            }
        }
    }

    private fun updateSelectionState(newRadioButton: RadioButton) {
        android.util.Log.d("ExamAnswerHandler", "updateSelectionState called")

        selectedRadioButton?.isChecked = false
        selectedContainer?.isSelected = false

        selectedRadioButton = newRadioButton
        selectedContainer = newRadioButton.parent as? LinearLayout
        selectedContainer?.isSelected = true

        android.util.Log.d("ExamAnswerHandler", "Visual state updated - container.isSelected = ${selectedContainer?.isSelected}")
    }

    @SuppressLint("NewApi")
    private fun startDrag(view: View, text: Char) {
        val clipData = ClipData.newPlainText("text", text.toString())
        val shadow = View.DragShadowBuilder(view)
        view.startDragAndDrop(clipData, shadow, view, 0)
        view.visibility = View.INVISIBLE
        Timber.d("Drag started for item: $text")
    }

    private fun handleDragEvent(
        view: View,
        event: DragEvent,
        index: Int,
        examNumber: ExamNumber,
        currentQuestion: ExamQuestion
    ): Boolean {
        when (event.action) {
            DragEvent.ACTION_DROP -> handleDropEvent(view, event, index, examNumber)
            DragEvent.ACTION_DRAG_ENDED -> handleDragEndEvent(event)
        }
        return true
    }

    private fun handleDropEvent(view: View, event: DragEvent, index: Int, examNumber: ExamNumber) {
        val draggedText = event.clipData.getItemAt(0).text.toString()
        val draggedOption = questionRenderer.optionTexts.find { it.text == draggedText.first() }

        android.util.Log.d("ExamAnswerHandler", "Drop event - draggedText: $draggedText, target index: $index")

        draggedOption?.let {
            updateSelectedOptionIds(index, it)

            // Update UI dynamically based on stored selections
            updateTargetViewText(view as TextView, index)

            val option = addSelectedOption()
            val newAnswer = examNumber.copy(pilihan = option)

            // Debug logging
            android.util.Log.d("ExamAnswerHandler", "Matching - Original examNumber: soalId=${examNumber.soalId}, nomorSoal=${examNumber.nomorSoal}")
            android.util.Log.d("ExamAnswerHandler", "Matching - New answer: soalId=${newAnswer.soalId}, nomorSoal=${newAnswer.nomorSoal}, pilihan=${newAnswer.pilihan}")

            onAnswerChanged(newAnswer)
        }
    }

    private fun handleDragEndEvent(event: DragEvent) {
        val draggedView = event.localState as View
        draggedView.visibility = View.VISIBLE
        Timber.d("Drag ended")
    }

    private fun onTargetViewClick(
        dropLayout: LinearLayout,
        targetView: TextView,
        examNumber: ExamNumber
    ) {
        val targetIndex = layoutExam.optionsContainer.indexOfChild(dropLayout)
        removeLastSelectedOption(targetIndex, targetView)

        val option = addSelectedOption()
        val newAnswer = examNumber.copy(pilihan = option)
        onAnswerChanged(newAnswer)
    }

    private fun addSelectedOption(): String {
        if (selectedOptionMappings.isEmpty()) return ""

        val formattedText = selectedOptionMappings.entries.flatMap { entry ->
            entry.value.map { value -> "[${entry.key},$value]" }
        }.joinToString(",")

        return formattedText
    }

    private fun removeLastSelectedOption(targetIndex: Int, targetView: TextView) {
        val dropLabelId = questionRenderer.dropItems[targetIndex].id

        // Check if there are answers to remove
        if (selectedOptionMappings[dropLabelId]?.isNotEmpty() == true) {
            selectedOptionMappings[dropLabelId]?.let {
                selectedOptionMappings[dropLabelId]?.removeAt(it.lastIndex)
            }
        }

        // Update UI dynamically
        updateTargetViewText(targetView, targetIndex)
    }

    private fun updateSelectedOptionIds(targetIndex: Int, draggedOption: Option) {
        val dropLabelId = questionRenderer.dropItems[targetIndex].id // Get the drop label ID

        // If there is no list yet, create one
        if (!selectedOptionMappings.containsKey(dropLabelId)) {
            selectedOptionMappings[dropLabelId] = mutableListOf()
        }

        // Add the new selection if it's not already present
        if (!selectedOptionMappings[dropLabelId]!!.contains(draggedOption.id)) {
            selectedOptionMappings[dropLabelId]!!.add(draggedOption.id)
        }
    }

    private fun updateTargetViewText(targetView: TextView, targetIndex: Int) {
        val dropLabelId = questionRenderer.dropItems[targetIndex].id
        val selectedOptions = selectedOptionMappings[dropLabelId] ?: emptyList()

        val newText = selectedOptions.joinToString("\n") { optionId ->
            questionRenderer.optionTexts.find { it.id == optionId }?.text.toString()
        }

        targetView.apply {
            text = newText
            textSize = 20f
            gravity = Gravity.CENTER
            // Set background depending on whether the text is empty or not
            background = ContextCompat.getDrawable(
                context,
                if (newText.isNotEmpty()) R.drawable.match_option_background else R.drawable.match_target_box
            )
            setTextColor(Color.WHITE)
        }
    }

    private fun updateUIWithSelections() {
        // Loop through the drop items (which correspond to the drop targets)
        questionRenderer.dropItems.forEachIndexed { index, dropItem ->
            val dropLabelId = dropItem.id
            val selectedOptions = selectedOptionMappings[dropLabelId] ?: emptyList()

            // Find the layout for this drop target in the options container
            val optionLayout =
                layoutExam.optionsContainer.getChildAt(index) as LinearLayout

            // Get the target TextView from this layout (it's the 3rd child in the LinearLayout based on your setup)
            val targetView =
                optionLayout.getChildAt(2) as TextView  // The target view is added as the 3rd view in the layout

            // Update the target view with the selected options
            val newText = selectedOptions.joinToString("\n") { optionId ->
                questionRenderer.optionTexts.find { it.id == optionId }?.text.toString()
            }

            targetView.apply {
                text = newText
                textSize = 20f
                gravity = Gravity.CENTER
                // Set background depending on whether the text is empty or not
                background = ContextCompat.getDrawable(
                    context,
                    if (newText.isNotEmpty()) R.drawable.match_option_background else R.drawable.match_target_box
                )
                setTextColor(Color.WHITE)
            }
        }
    }
}
