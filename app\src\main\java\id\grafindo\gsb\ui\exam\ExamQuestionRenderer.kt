package id.grafindo.gsb.ui.exam

import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.style.RelativeSizeSpan
import android.text.style.StyleSpan
import android.text.style.SubscriptSpan
import android.text.style.SuperscriptSpan
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.Button
import android.widget.CheckBox
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import androidx.core.content.ContextCompat
import id.grafindo.gsb.R
import id.grafindo.gsb.data.remote.json.TipeSoal
import id.grafindo.gsb.databinding.FragmentExamQuestionBinding
import id.grafindo.gsb.domain.model.ExamAnswer
import id.grafindo.gsb.domain.model.ExamQuestion
import id.grafindo.gsb.ext.hideView
import id.grafindo.gsb.ext.showView
import org.jsoup.Jsoup
import org.jsoup.nodes.Element

data class Option(val id: Long, val text: Char)

class ExamQuestionRenderer(
    private val context: Context,
    private val layoutExam: FragmentExamQuestionBinding
) {
    private val webViewCache = mutableMapOf<String, WebView>()
    private val optionButtonSize = 42 // dp
    private val optionButtonSizeInPx by lazy { dpToPx(optionButtonSize) }

    lateinit var optionTexts: List<Option>
    lateinit var dropItems: List<ExamAnswer>

    fun renderQuestion(data: ExamQuestion) {
        if (data.tipeSoal == TipeSoal.Menjodohkan) {
            optionTexts = data.jawabanmatchingright.mapIndexed { index, examAnswer ->
                Option(examAnswer.id, 'A' + index)
            }
            dropItems = data.dataJawaban
        }

        val questionView = when (data.tipeSoal) {
            TipeSoal.Menjodohkan -> layoutExam.tvMatchQuestion
            else -> layoutExam.tvQuestion
        }
        questionView.loadQuestionHtml(data.generateQuestionHtmlString())
    }

    fun setupAnswerOptions(data: ExamQuestion) {
        with(layoutExam) {
            when (data.tipeSoal) {
                TipeSoal.PilihanGanda -> {
                    radioGroupChoices.removeAllViews()
                    nestedScrollView.showView()
                    radioGroupChoices.showView()
                    checkboxGroupChoices.hideView()
                    matchOption.hideView()
                    setupRadioButtons(data)
                }

                TipeSoal.PilihanGandaKompleks -> {
                    checkboxGroupChoices.removeAllViews()
                    nestedScrollView.showView()
                    checkboxGroupChoices.showView()
                    radioGroupChoices.hideView()
                    matchOption.hideView()
                    setupCheckboxes(data)
                }

                else -> {
                    optionsContainer.removeAllViews()
                    targetsContainer.removeAllViews()
                    matchOption.showView()
                    nestedScrollView.hideView()
                    setupDropTargets()
                    setupOptions(data)
                }
            }
        }
    }

    private fun setupRadioButtons(data: ExamQuestion) {
        layoutExam.radioGroupChoices.apply {
            data.dataJawaban.forEachIndexed { index, answer ->
                val (radioButton, container) = createRadioButton(
                    index,
                    answer.generateAnswerHtmlString()
                )

                // Add the container to RadioGroup, but ensure RadioButton is properly configured
                addView(container)
            }
        }
    }

    private fun setupCheckboxes(data: ExamQuestion) {
        layoutExam.checkboxGroupChoices.apply {
            data.dataJawaban.forEach { answer ->
                val (checkBox, webView) = createCheckbox(answer.generateAnswerHtmlString())
                if (webView != null) {
                    addView(webView) // webView is the container in this case
                } else {
                    addView(checkBox)
                }
            }
        }
    }

    private fun setupDropTargets() = dropItems.forEachIndexed(::createDropTarget)

    private fun setupOptions(data: ExamQuestion) =
        data.jawabanmatchingright.forEachIndexed { index, answer ->
            createOptionButton(index, answer)
        }

    private fun convertHtmlToSpannable(element: Element): SpannableStringBuilder {
        val output = SpannableStringBuilder()

        for (node in element.childNodes()) {
            when {
                node.nodeName() == "#text" -> {
                    val text = node.toString().replace("&nbsp;", " ")
                    output.append(text)
                }

                node.nodeName() == "sup" || node.nodeName() == "sub" -> {
                    val isSup = node.nodeName() == "sup"
                    val rawText = (node as? Element)?.text() ?: node.toString()
                    val text = Jsoup.parse(rawText).text()

                    val start = output.length
                    output.append(text)
                    val end = output.length

                    val span = if (isSup) SuperscriptSpan() else SubscriptSpan()
                    output.setSpan(span, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    output.setSpan(
                        RelativeSizeSpan(0.75f),
                        start,
                        end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }

                node.nodeName() == "em" -> {
                    val childText = convertHtmlToSpannable(node as Element)
                    val start = output.length
                    output.append(childText)
                    val end = output.length
                    output.setSpan(
                        StyleSpan(Typeface.ITALIC),
                        start,
                        end,
                        Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }

                node.nodeName() == "br" -> {
                    output.append("\n")
                }

                else -> {
                    if (node is Element) {
                        output.append(convertHtmlToSpannable(node))
                    }
                }
            }
        }

        return output
    }

    fun createRadioButton(
        index: Int,
        answer: String
    ): Pair<RadioButton, LinearLayout> {
        val document = Jsoup.parse(answer)
        val paragraphs = document.body().children().fold(SpannableStringBuilder()) { acc, element ->
            acc.append(convertHtmlToSpannable(element)).append(" ")
        }
        val containsImage = document.select("img").isNotEmpty()

        val radioButton = RadioButton(context).apply {
            id = View.generateViewId()
            text = if (containsImage) "${('A' + index)}." else null
            textSize = 16f
            typeface = Typeface.SERIF
            setTextColor(Color.BLACK)
            layoutParams = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                gravity = Gravity.CENTER_VERTICAL
            }
            // Note: No individual listener - RadioGroup will handle selection
        }

        val container: LinearLayout = if (containsImage) {
            val webView = createWebView(answer)
            LinearLayout(context).apply {
                orientation = LinearLayout.VERTICAL
                isClickable = true
                isFocusable = true
                background = ContextCompat.getDrawable(context, R.drawable.option_background)
                setPadding(dpToPx(12), dpToPx(12), dpToPx(12), dpToPx(12))
                layoutParams = ViewGroup.MarginLayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                ).apply {
                    bottomMargin = dpToPx(16)
                }

                addView(radioButton)
                addView(webView)

                // Note: Click listener will be set by ExamAnswerHandler to handle proper radio group behavior
            }
        } else {
            val labelText = TextView(context).apply {
                text = "${('A' + index)}."
                textSize = 16f
                typeface = Typeface.SERIF
                setTextColor(Color.BLACK)
                layoutParams = LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                ).apply {
                    setMargins(dpToPx(8), 0, dpToPx(8), 0)
                    gravity = Gravity.CENTER_VERTICAL
                }
            }

            val answerText = TextView(context).apply {
                text = paragraphs
                textSize = 16f
                typeface = Typeface.SERIF
                setTextColor(Color.BLACK)
                layoutParams = LinearLayout.LayoutParams(
                    0,
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    1f
                ).apply {
                    gravity = Gravity.CENTER_VERTICAL
                }
            }

            LinearLayout(context).apply {
                orientation = LinearLayout.HORIZONTAL
                isClickable = true
                isFocusable = true
                background = ContextCompat.getDrawable(context, R.drawable.option_background)
                setPadding(dpToPx(12), dpToPx(14), dpToPx(12), dpToPx(14))
                layoutParams = ViewGroup.MarginLayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                ).apply {
                    bottomMargin = dpToPx(16)
                }

                addView(radioButton)
                addView(labelText)
                addView(answerText)

                // Note: Click listener will be set by ExamAnswerHandler to handle proper radio group behavior
            }
        }

        return Pair(radioButton, container)
    }

    fun createCheckbox(answer: String): Pair<CheckBox, LinearLayout?> {
        val document = Jsoup.parse(answer)
        val paragraphs = document.body().children().fold(SpannableStringBuilder()) { acc, element ->
            acc.append(convertHtmlToSpannable(element)).append(" ")
        }
        val containsImage = document.select("img").isNotEmpty()

        val checkBox = CheckBox(context).apply {
            id = View.generateViewId()
            textSize = 16f
            text = if (containsImage) "" else SpannableStringBuilder().apply { append(paragraphs) }
            if (!containsImage) setPadding(dpToPx(0), dpToPx(14), dpToPx(16), dpToPx(14))

            layoutParams = ViewGroup.MarginLayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                if (!containsImage) bottomMargin = dpToPx(16)
            }

            typeface = Typeface.SERIF
            background = if (!containsImage) {
                ContextCompat.getDrawable(context, R.drawable.option_background)
            } else {
                null
            }
        }

        val webView = if (containsImage) {
            createWebView(answer)
        } else {
            null
        }

        // Wrap checkbox and webView in a container if it has an image
        if (containsImage) {
            val container = LinearLayout(context).apply {
                orientation = LinearLayout.VERTICAL
                isClickable = true
                isFocusable = true
                background = ContextCompat.getDrawable(context, R.drawable.option_background)
                setPadding(dpToPx(12), dpToPx(12), dpToPx(12), dpToPx(12))
                layoutParams = ViewGroup.MarginLayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
                ).apply { bottomMargin = dpToPx(16) }

                addView(checkBox)
                webView?.let { addView(it) }

                // Click listener: toggle checkbox and selection state
                setOnClickListener {
                    checkBox.isChecked = !checkBox.isChecked
                }
            }

            checkBox.setOnCheckedChangeListener { _, isChecked ->
                container.isSelected = isChecked
            }

            return Pair(CheckBox(context).apply { visibility = View.GONE }, container)
        }

        return Pair(checkBox, null)
    }

    private fun createDropTarget(index: Int, data: ExamAnswer) {
        val dropLayout = createDropLayout()
        val button = createIndexView(index)
        val textView = createDropWebView(data.generateTargetHtmlString())
        val targetView = createTargetView()

        if (textView.parent != null) {
            (textView.parent as ViewGroup).removeView(textView)
        }

        dropLayout.addView(button)
        dropLayout.addView(textView)
        dropLayout.addView(targetView)

        layoutExam.optionsContainer.addView(dropLayout)
    }

    private fun createDropLayout(): LinearLayout {
        return LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply { bottomMargin = dpToPx(12) }
        }
    }

    private fun createIndexView(index: Int): Button {
        return Button(context).apply {
            text = context.getString(R.string.match_button_label, (index + 1).toString())
            textSize = 20f
            background = ContextCompat.getDrawable(context, R.drawable.match_left_side_background)
            layoutParams = LinearLayout.LayoutParams(optionButtonSizeInPx, optionButtonSizeInPx)
                .apply { setPadding(dpToPx(6), 13.5.toInt(), dpToPx(6), 13.5.toInt()) }
            setTextColor(ContextCompat.getColor(context, R.color.cyan_500))
        }
    }

    private fun createDropWebView(htmlContent: String): WebView {
        return webViewCache.getOrPut(htmlContent) {
            WebView(context).apply {
                loadQuestionHtml(htmlContent)

                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    1f
                ).apply {
                    marginStart = dpToPx(10)
                    marginEnd = dpToPx(10)
                }
            }
        }
    }

    private fun createTargetView(): TextView {
        return TextView(context).apply {
            textSize = 18f
            background = ContextCompat.getDrawable(context, R.drawable.match_target_box)
            minimumHeight = optionButtonSizeInPx
            layoutParams = LinearLayout.LayoutParams(
                optionButtonSizeInPx,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                0f
            )
            setTextColor(Color.WHITE)
        }
    }

    private fun createOptionButton(index: Int, data: ExamAnswer) {
        val optionLayout = createOptionLayout()
        val button = createOptionButtonView(index)
        val label = createOptionTextView(data.generateOptionHtmlString())

        if (label.parent != null) {
            (label.parent as ViewGroup).removeView(label)
        }

        optionLayout.addView(button)
        optionLayout.addView(label)

        layoutExam.targetsContainer.addView(optionLayout)
    }

    private fun createOptionLayout(): LinearLayout {
        return LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ).apply { bottomMargin = dpToPx(12) }
        }
    }

    private fun createOptionButtonView(index: Int): Button {
        return Button(context).apply {
            val label = 'A' + index
            text = context.getString(R.string.match_button_label, label.toString())
            textSize = 20f
            background = ContextCompat.getDrawable(context, R.drawable.match_option_background)
            layoutParams = LinearLayout.LayoutParams(optionButtonSizeInPx, optionButtonSizeInPx)
                .apply { setPadding(dpToPx(6), 13.5.toInt(), dpToPx(6), 13.5.toInt()) }
            setTextColor(Color.WHITE)
            // Note: Long click listener will be set by the answer handler
        }
    }

    private fun createOptionTextView(htmlContent: String): WebView {
        return webViewCache.getOrPut(htmlContent) {
            WebView(context).apply {
                loadQuestionHtml(htmlContent)

                minimumHeight = optionButtonSizeInPx
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    1f
                ).apply { marginStart = dpToPx(10) }
            }
        }
    }

    private fun createWebView(answer: String): WebView {
        return WebView(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT
            )
            loadQuestionHtml(answer)
        }
    }

    private fun WebView.loadQuestionHtml(htmlContent: String) {
        setBackgroundColor(Color.TRANSPARENT)
        loadDataWithBaseURL(
            "file:///android_res/",
            htmlContent,
            "text/html; charset=utf-8",
            "utf-8",
            null
        )
        settings.apply {
            builtInZoomControls = true
            displayZoomControls = false
        }
    }

    private fun dpToPx(dp: Int): Int = (dp * context.resources.displayMetrics.density).toInt()
}
