// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    id("com.github.ben-manes.versions").version(LibsVersion.VERSIONS)
    id("io.gitlab.arturbosch.detekt").version(LibsVersion.DETEKT)
    id("org.jlleitschuh.gradle.ktlint").version(LibsVersion.KT_LINT_GRADLE)
    id("com.google.devtools.ksp") version "2.0.0-1.0.21" apply false
}

buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath(Libs.ANDROID_GRADLE_PLUGIN)
        classpath(kotlin("gradle-plugin", version = LibsVersion.KOTLIN))
        classpath(Libs.AndroidX.Navigation.SAFE_ARGS_GRADLE_PLUGIN)
        // NOTE: Do not place your application dependencies here;
        // they belong in the individual module build.gradle files
        classpath(Libs.GOOGLE_SERVICE)
        classpath(Libs.FIREBASE_CRASHLYTICS)
        // classpath(Libs.lint)
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven("https://jitpack.io")
        maven("https://maven.ghostscript.com") {
            isAllowInsecureProtocol = true
        }
        maven("http://maven.ghostscript.com") {
            isAllowInsecureProtocol = true
        }
    }
}

tasks.register("clean", Delete::class) {
    delete(rootProject.layout.buildDirectory)
}

// ben-manes versions checking
fun isNonStable(version: String): Boolean {
    val stableKeyword = listOf("RELEASE", "FINAL", "GA").any {
        version.uppercase()
            .contains(it)
    }
    val regex = "^[0-9,.v-]+(-r)?$".toRegex()
    val isStable = stableKeyword || regex.matches(version)
    return isStable.not()
}

tasks.withType<com.github.benmanes.gradle.versions.updates.DependencyUpdatesTask> {
    // reject all non stable versions
    rejectVersionIf {
        isNonStable(candidate.version)
    }

    // optional parameters
    checkForGradleUpdate = true
    outputFormatter = "json"
    outputDir = "build/dependencyUpdates"
    reportfileName = "report"
}
