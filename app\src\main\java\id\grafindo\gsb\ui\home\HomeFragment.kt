package id.grafindo.gsb.ui.home

import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.widget.EditText
import androidx.activity.addCallback
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.SearchView
import androidx.appcompat.widget.Toolbar
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import coil.ImageLoader
import com.afollestad.materialdialogs.MaterialDialog
import id.grafindo.gsb.R
import id.grafindo.gsb.databinding.FragmentHomeBinding
import id.grafindo.gsb.ext.setOnQueryTextChangeListener
import id.grafindo.gsb.ext.viewBinding
import id.grafindo.gsb.ui.MainActivity
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import timber.log.Timber

class HomeFragment : Fragment(R.layout.fragment_home) {
    private val binding by viewBinding<FragmentHomeBinding>()
    private val viewModel by viewModel<HomeViewModel>()
    private val coilLoader by inject<ImageLoader>()

    private val toolbar get() = binding.toolbar
    private val swipeRefresh get() = binding.swipeRefresh
    private val layoutEmpty get() = binding.layEmpty
    private val layoutShimmer get() = binding.layShimmer
    private val layoutMain get() = binding.layMain
    private val layoutInfo get() = binding.layInfo

    private lateinit var rvBannerAdapter: HomeBannerAdapter
    private lateinit var rvFirstCategoryAdapter: CatalogAdapter
    private lateinit var rvSecondCategoryAdapter: CatalogAdapter
    private var shimmerJob: Job? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        (activity as MainActivity).showBottomNavigation()

        with(toolbar) {
            title = getString(R.string.title_grafindo)
            subtitle = getString(R.string.subtitle_grafindo)
            setSubtitleTextAppearance(requireContext(), R.style.TextAppearance_GSB_Overline_Alt)
            setSubtitleTextColor(Color.WHITE)
            navigationIcon = AppCompatResources.getDrawable(requireContext(), R.drawable.ic_logo)
            setupMenu(this)
        }

        requireActivity().onBackPressedDispatcher.addCallback {
            MaterialDialog(requireContext()).show {
                title(text = "Apakah Anda yakin ingin keluar?")
                positiveButton(R.string.text_dialog_yes) {
                    requireActivity().finish()
                }
                negativeButton(R.string.text_dialog_no) {
                    dismiss()
                }
            }
        }
        setupList()
        toggleShimmer(false)
        toggleLayoutEmpty(true)
        viewModel.netStatus.observe(viewLifecycleOwner) {
            if (it) {
                Timber.d("Internet available")
                toggleShimmer(true)
                toggleLayoutEmpty(false)
                viewModel.fetchHomeState()
            } else {
                Timber.d("Internet unavailable")
                toggleShimmer(false)
                toggleLayoutEmpty(true)
            }
        }
        swipeRefresh?.setOnRefreshListener {
            viewModel.fetchHomeContent()
            toggleShimmer(true)
        }

        viewModel.firstCategories.observe(viewLifecycleOwner) { data ->
            data?.let { rvFirstCategoryAdapter.submitList(data.map { it.copy() }) }
        }

        viewModel.secondCategories.observe(viewLifecycleOwner) { data ->
            data?.let { rvSecondCategoryAdapter.submitList(data.map { it.copy() }) }
        }

        viewModel.imageUrls.observe(viewLifecycleOwner) { images ->
            rvBannerAdapter = HomeBannerAdapter(
                images,
                coilLoader
            )
            with(binding.vpBanner) {
                adapter = rvBannerAdapter
            }
            Timber.d("imageBannerUpdated")
            shimmerJob?.cancel()
            shimmerJob = viewLifecycleOwner.lifecycleScope.launch {
                try {
                    delay(1000)
                    if (isAdded && view != null) {
                        toggleShimmer(false)
                        Timber.d("shimmerToggled")
                    }
                } catch (e: kotlinx.coroutines.CancellationException) {
                    // Job was cancelled, ignore
                }
            }
        }

        viewModel.errorMessage.observe(viewLifecycleOwner) {
            it?.let {
                if (it.isNotEmpty()) {
                    // snackbar(it, anchorViewId = R.id.bottom_nav)
                    toggleShimmer(false)
                }
            }
        }
    }

    private fun setupList() {
        rvFirstCategoryAdapter = CatalogAdapter(
            coilLoader
        ) { id, slug ->
            navigateToDetailFragment(id, slug)
        }
        with(binding.rvCategory1) {
            adapter = rvFirstCategoryAdapter
            layoutManager =
                LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        }

        rvSecondCategoryAdapter = CatalogAdapter(
            coilLoader
        ) { id, slug ->
            navigateToDetailFragment(id, slug)
        }
        with(binding.rvCategory2) {
            adapter = rvSecondCategoryAdapter
            layoutManager = LinearLayoutManager(
                requireContext(),
                LinearLayoutManager.HORIZONTAL,
                false
            )
        }
    }

    private fun toggleShimmer(visible: Boolean = false) {
        if (!isAdded || view == null) return
        layoutShimmer?.isVisible = visible
        layoutMain?.isVisible = !visible
        layoutInfo?.isVisible = !visible
        swipeRefresh?.isRefreshing = visible
    }

    private fun navigateToDetailFragment(parentId: Int, slug: String) {
        findNavController().navigate(
            HomeFragmentDirections.actionHomeToCatalogDetail(parentId, slug)
        )
    }

    private fun navigateToSearchProduct(query: String) {
        findNavController().navigate(
            HomeFragmentDirections.actionHomeToProductList(0, query, "")
        )
    }

    private fun toggleLayoutEmpty(visible: Boolean) {
        layoutEmpty.isVisible = visible
        with(toolbar) {
            if (visible) {
                menu.clear()
            } else {
                setupMenu(this)
            }
        }
    }

    private fun setupMenu(toolbar: Toolbar) {
        with(toolbar) {
            menu.clear()
            inflateMenu(R.menu.search_product)
            val searchItem = menu.findItem(R.id.action_search)
            val searchView = searchItem.actionView as SearchView
            searchView.findViewById<EditText>(androidx.appcompat.R.id.search_src_text)
                .setTextColor(Color.WHITE)
            searchView.queryHint = getString(R.string.text_search_book)

            setOnQueryTextChangeListener(searchView, true) {
                if (!it.isNullOrBlank()) navigateToSearchProduct(it)
                true
            }
        }
    }

    override fun onDestroyView() {
        shimmerJob?.cancel()
        shimmerJob = null
        super.onDestroyView()
    }
}
