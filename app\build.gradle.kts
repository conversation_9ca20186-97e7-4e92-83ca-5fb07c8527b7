import java.util.Properties

plugins {
    id("com.android.application")
    kotlin("android")
    kotlin("plugin.serialization") version LibsVersion.KOTLIN
    id("com.google.devtools.ksp")
    id("androidx.navigation.safeargs.kotlin")
    id("org.jlleitschuh.gradle.ktlint")
    id("io.gitlab.arturbosch.detekt")
    id("com.google.gms.google-services")
    id("com.google.firebase.crashlytics")
    id("kotlin-android")
}

val localProperties = Properties()
localProperties.load(file("$rootDir/local.properties").inputStream())

android {
    namespace = "id.grafindo.gsb"
    compileSdk = AndroidConfig.COMPILE_SDK_VERSION
    buildToolsVersion = AndroidConfig.BUILD_TOOLS_VERSION
    ndkVersion = AndroidConfig.NDK_VERSION

    defaultConfig {
        applicationId = "id.grafindo.gsb"
        minSdk = AndroidConfig.MIN_SDK_VERSION
        targetSdk = AndroidConfig.TARGET_SDK_VERSION
        versionCode = ProjectConfig.VERSION_CODE
        versionName = ProjectConfig.VERSION_NAME

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        // build config
        buildConfigField(
            "String",
            "wpCustomerKey",
            localProperties["wp.customer.key"] as String
        )
        buildConfigField(
            "String",
            "wpCustomerSecret",
            localProperties["wp.customer.secret"] as String
        )

        ksp {
            arg("room.schemaLocation", "$projectDir/schemas")
        }
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
    testOptions {
        unitTests.isReturnDefaultValues = true
    }
    buildTypes {
        getByName("debug") {
            applicationIdSuffix = ".debug"
        }
        getByName("release") {
            isMinifyEnabled = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            // signingConfig = signingConfigs.getByName("release")
        }
    }

    compileOptions {
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility(JavaVersion.VERSION_1_8)
        targetCompatibility(JavaVersion.VERSION_1_8)
    }
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8.toString()
        freeCompilerArgs = freeCompilerArgs + listOf(
            "-Xopt-in=kotlin.ExperimentalStdlibApi",
            "-Xopt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-Xopt-in=kotlin.RequiresOptIn"
        )
    }
}

dependencies {

    implementation("androidx.annotation:annotation:1.9.1")
    implementation("androidx.slice:slice-builders:1.0.0")
    coreLibraryDesugaring(Libs.JDK_DESUGAR)
    // core
    implementation("org.jetbrains.kotlin:kotlin-stdlib:2.1.10")
    implementation(Libs.AndroidX.CORE)
    // implementation(Libs.AndroidX.multiDex)
    implementation(Libs.AndroidX.APP_COMPAT)
    implementation(Libs.AndroidX.CONSTRAINT_LAYOUT)

    // coroutines
    implementation(Libs.Kotlin.Coroutines.CORE)
    implementation(Libs.Kotlin.Coroutines.ANDROID)

    // ktx
    implementation(Libs.AndroidX.KTX.ACTIVITY)
    implementation(Libs.AndroidX.KTX.FRAGMENT)
    implementation(Libs.AndroidX.KTX.PREFERENCES)
    implementation(Libs.AndroidX.KTX.ROOM)

    // lifecycle
    implementation(Libs.AndroidX.Lifecycle.RUNTIME)
    implementation(Libs.AndroidX.Lifecycle.LIVE_DATA)
    implementation(Libs.AndroidX.Lifecycle.VIEW_MODEL)
    ksp(Libs.AndroidX.Lifecycle.COMPILER)

    // navigation
    implementation(Libs.AndroidX.Navigation.NAV_FRAGMENT)
    implementation(Libs.AndroidX.Navigation.NAV_UI)

    // data
    implementation(Libs.AndroidX.Room.RUNTIME)
    ksp(Libs.AndroidX.Room.COMPILER)
    implementation(Libs.AndroidX.DATA_STORE)
    implementation(Libs.AndroidX.WORK_MANAGER)

    // networking
    implementation(Libs.Network.KOTLINX_SERIALIZATION)
    implementation(Libs.Square.RETROFIT)
    implementation(Libs.Network.RETROFIT_KOTLINX_SERIALIZATION_CONVERTER)
    implementation(Libs.Square.OKHTTP_LOGGING)

    // ui
    implementation(Libs.Google.MATERIAL)
    implementation(Libs.AndroidX.SWIPE_REFRESH)
    implementation(Libs.AndroidX.VIEW_PAGER2)
    implementation(Libs.AndroidX.PAGING)
    implementation(Libs.CoilKt.CORE)
    implementation(Libs.MaterialDialog.CORE)
    implementation(Libs.OTP_VIEW)

    // firebase
    implementation(platform(Libs.Google.Firebase.BOM))
    implementation(Libs.Google.Firebase.ANALYTICS)
    implementation(Libs.Google.Firebase.CRASHLYTICS)
    implementation(Libs.Google.Firebase.MESSAGING)
    
    // logging
    implementation("com.jakewharton.timber:timber:5.0.1")

    implementation("com.google.code.gson:gson:2.10.1")
    

    // pdf
    api("com.artifex.mupdf:viewer:1.15.0")

    // di
    implementation(Libs.Koin.CORE)

    // logging
    implementation(Libs.TIMBER)

    // shimmer
    implementation(Libs.SHIMMER)

    // image processing
    implementation(Libs.COMPRESSOR)

    // HTML parser
    implementation("org.jsoup:jsoup:1.19.1")

    // unit testing
    testImplementation(Libs.Testing.JUNIT)

    // instrumentation testing
    androidTestImplementation(Libs.Testing.JUNIT_EXT)

    // espresso
    androidTestImplementation(Libs.Testing.Espresso.CORE)
}

// ktlint setup
ktlint {
    verbose.set(true)
    android.set(true)

    // Uncomment below line and run .\gradlew ktlintCheck to see check ktlint experimental rules
    // enableExperimentalRules.set(true)

    reporters {
        reporter(org.jlleitschuh.gradle.ktlint.reporter.ReporterType.CHECKSTYLE)
    }

    filter {
        exclude("**/generated/**")
        include("**/kotlin/**")
    }
}

// detekt setup
detekt {
    config.from(files("$rootDir/detekt.yml"))
    parallel = true
}
