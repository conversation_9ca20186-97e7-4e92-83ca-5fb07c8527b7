package id.grafindo.gsb.ui.exam.submit

import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import id.grafindo.gsb.data.preference.AppPreference
import id.grafindo.gsb.data.remote.NetworkResult
import id.grafindo.gsb.domain.model.ExamProgress
import id.grafindo.gsb.domain.model.ExamScore
import id.grafindo.gsb.domain.model.ExamSubmit
import id.grafindo.gsb.domain.model.ExamSubmitAnswer
import id.grafindo.gsb.domain.usecase.ExamUseCase
import id.grafindo.gsb.ext.formatDuration
import id.grafindo.gsb.ext.gsbDateFormatter
import id.grafindo.gsb.ext.map
import id.grafindo.gsb.ext.toMillis
import id.grafindo.gsb.ext.toMinutes
import id.grafindo.gsb.util.base.ApiViewModel
import id.grafindo.gsb.util.network.NetworkStatusTracker
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.LocalDate
import java.util.concurrent.TimeUnit

class ExamSubmitViewModel(
    bookId: Long,
    private val examId: Long,
    val prefs: AppPreference,
    networkStatusTracker: NetworkStatusTracker,
    private val useCase: ExamUseCase
) : ApiViewModel(prefs) {
    sealed class UIEvent {
        data object ExamCorrupted : UIEvent()
        data object ExamLocked : UIEvent()
        data class OnSubmitSuccess(val data: ExamScore) : UIEvent()
    }

    val netStatus = networkStatusTracker.networkStatus
        .map(
            onAvailable = { true },
            onUnavailable = { false }
        )
        .asLiveData()
    val userToken = prefs.userPreferenceFlow.map { it.token }.asLiveData()
    val data = useCase.getExamBookDetail(bookId)
        .combine(useCase.getExam(bookId, examId)) { book, exam ->
            Pair(book, exam)
        }.asLiveData()
    private val _countDownText = MutableLiveData<String>()
    val countDownText: LiveData<String> get() = _countDownText
    private lateinit var timer: CountDownTimer
    private var elapsedTime = 0L

    private val _errorMessage = MutableStateFlow("")
    val errorMessage = _errorMessage.asLiveData()

    private val eventChannel = Channel<UIEvent>(Channel.BUFFERED)
    val eventsFlow = eventChannel.receiveAsFlow()

    private val _progressData = MutableLiveData<ExamProgress?>()
    val progressData: LiveData<ExamProgress?> get() = _progressData

    init {
        viewModelScope.launch {
            val localProgress = useCase.getLocalExamProgress(examId)
            if (localProgress == null) {
                eventChannel.send(UIEvent.ExamCorrupted)
            } else {
                _progressData.postValue(localProgress)
                if (localProgress.timeLeft > 0) {
                    resumeExam(localProgress)
                } else {
                    lockExam()
                }
            }
        }
    }

    private fun resumeExam(data: ExamProgress) {
        timer =
            object : CountDownTimer(data.timeLeft, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    generateCountDownText(millisUntilFinished)
                }

                override fun onFinish() {
                    lockExam()
                }
            }
        timer.start()
    }

    // you can't go back cause time is up!
    private fun lockExam() {
        viewModelScope.launch {
            eventChannel.send(UIEvent.ExamLocked)
        }
    }

    private fun generateCountDownText(duration: Long) {
        val formattedDuration = formatDuration(duration)
        elapsedTime = duration
        _countDownText.postValue(formattedDuration)
    }

    fun getTimeLeft() = elapsedTime

    fun submitExam(token: String) {
        Timber.d("submitExam($token)")
        viewModelScope.launch {
            val dataValue = data.value
            if (dataValue == null) {
                Timber.e("submitExam: data.value is null")
                _errorMessage.value = "Data ujian tidak tersedia"
                return@launch
            }

            val bookData = dataValue.first
            val examData = dataValue.second
            val progressData = _progressData.value
            if (progressData == null) {
                Timber.e("submitExam: progressData is null")
                _errorMessage.value = "Data progress ujian tidak tersedia"
                return@launch
            }
            val listExamNumber = progressData.decodeFromProgressString()

            val timeDoExam = (examData.waktu.toMillis(TimeUnit.MINUTES) - progressData.timeLeft)

            val hours = (timeDoExam / (1000 * 60 * 60)) % 24
            val minutes = (timeDoExam / (1000 * 60)) % 60
            val seconds = (timeDoExam / 1000) % 60

            val submitData = ExamSubmit(
                jawaban = listExamNumber.map {
                    ExamSubmitAnswer(
                        soalId = it.soalId,
                        jawaban = it.pilihan
                    )
                },
                emailGuru = bookData.ebook.emailGuru,
                tanggal = LocalDate.now().format(gsbDateFormatter),
                elapsedTime = "$hours : $minutes :$seconds"
            )
            Timber.d("Submit -> $submitData")
            _errorMessage.value = ""
            useCase.submitExam(token, examId, submitData)
                .catch { _errorMessage.value = it.message ?: "" }
                .collect { result ->
                    when (result) {
                        is NetworkResult.Success ->
                            eventChannel.send(UIEvent.OnSubmitSuccess(result.data))

                        is NetworkResult.Error ->
                            _errorMessage.value = result.exception.message ?: ""
                    }
                }
        }
    }

    fun afterSubmitSuccess() {
        viewModelScope.launch {
            val progressData = _progressData.value
            if (progressData == null) {
                Timber.e("afterSubmitSuccess: progressData is null")
                return@launch
            }

            val data = progressData.copy(isSubmitted = true, is_pending = false)
            useCase.saveExamProgress(data)
        }
    }

    override fun onCleared() {
        super.onCleared()
        viewModelScope.cancel()
        if (this::timer.isInitialized) timer.cancel()
    }

    fun saveProgressOnNoInternet() {
        viewModelScope.launch {
            val dataValue = data.value
            if (dataValue == null) {
                Timber.e("saveProgressOnNoInternet: data.value is null")
                return@launch
            }

            val bookData = dataValue.first
            val examData = dataValue.second
            val progressData = _progressData.value
            if (progressData == null) {
                Timber.e("saveProgressOnNoInternet: progressData is null")
                return@launch
            }

            val data = progressData.copy(
                is_pending = true,
                isSubmitted = false,
                email_guru = bookData.ebook.emailGuru,
                tanggal = LocalDate.now().format(gsbDateFormatter),
                elapsed_time = (examData.waktu.toMillis(TimeUnit.MINUTES) - progressData.timeLeft)
                    .toMinutes(TimeUnit.MILLISECONDS).toString()
            )
            useCase.saveExamProgress(data)
        }
    }
}
