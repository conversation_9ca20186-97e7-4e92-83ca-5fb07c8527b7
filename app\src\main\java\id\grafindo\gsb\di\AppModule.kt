package id.grafindo.gsb.di

import android.app.Application
import android.content.Context
import androidx.room.Room
import coil.ImageLoader
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import id.grafindo.gsb.BuildConfig
import id.grafindo.gsb.data.AuthRepositoryImpl
import id.grafindo.gsb.data.BookRepositoryImpl
import id.grafindo.gsb.data.CatalogRepositoryImpl
import id.grafindo.gsb.data.ExamRepositoryImpl
import id.grafindo.gsb.data.db.AppDatabase
import id.grafindo.gsb.data.db.migration.MIGRATION_1_2
import id.grafindo.gsb.data.db.migration.MIGRATION_1_3
import id.grafindo.gsb.data.db.migration.MIGRATION_3_4
import id.grafindo.gsb.data.interactor.ActivationInteractor
import id.grafindo.gsb.data.interactor.AgreementInteractor
import id.grafindo.gsb.data.interactor.CatalogInteractor
import id.grafindo.gsb.data.interactor.CustomerCareInteractor
import id.grafindo.gsb.data.interactor.ExamInteractor
import id.grafindo.gsb.data.interactor.ForgotPasswordInteractor
import id.grafindo.gsb.data.interactor.HomeInteractor
import id.grafindo.gsb.data.interactor.LibraryInteractor
import id.grafindo.gsb.data.interactor.LoginInteractor
import id.grafindo.gsb.data.interactor.ProductInteractor
import id.grafindo.gsb.data.interactor.ProfileInteractor
import id.grafindo.gsb.data.interactor.UpdatePasswordInteractor
import id.grafindo.gsb.data.preference.AppPreference
import id.grafindo.gsb.data.remote.ApiService
import id.grafindo.gsb.data.remote.WPApiService
import id.grafindo.gsb.domain.repository.AuthRepository
import id.grafindo.gsb.domain.repository.BookRepository
import id.grafindo.gsb.domain.repository.CatalogRepository
import id.grafindo.gsb.domain.repository.ExamRepository
import id.grafindo.gsb.domain.usecase.ActivationUseCase
import id.grafindo.gsb.domain.usecase.AgreementUseCase
import id.grafindo.gsb.domain.usecase.CatalogUseCase
import id.grafindo.gsb.domain.usecase.CustomerCareUseCase
import id.grafindo.gsb.domain.usecase.ExamUseCase
import id.grafindo.gsb.domain.usecase.ForgotPasswordUseCase
import id.grafindo.gsb.domain.usecase.HomeUseCase
import id.grafindo.gsb.domain.usecase.LibraryUseCase
import id.grafindo.gsb.domain.usecase.LoginUseCase
import id.grafindo.gsb.domain.usecase.ProductUseCase
import id.grafindo.gsb.domain.usecase.ProfileUseCase
import id.grafindo.gsb.domain.usecase.UpdatePasswordUseCase
import id.grafindo.gsb.ui.agreements.AgreementViewModel
import id.grafindo.gsb.ui.auth.activation.ActivationViewModel
import id.grafindo.gsb.ui.auth.forgotpassword.ForgotPasswordViewModel
import id.grafindo.gsb.ui.auth.login.LoginViewModel
import id.grafindo.gsb.ui.exam.ExamViewModel
import id.grafindo.gsb.ui.exam.list.ExamListViewModel
import id.grafindo.gsb.ui.exam.preface.ExamPrefaceViewModel
import id.grafindo.gsb.ui.exam.submit.ExamSubmitViewModel
import id.grafindo.gsb.ui.home.HomeViewModel
import id.grafindo.gsb.ui.home.catalog.CatalogDetailViewModel
import id.grafindo.gsb.ui.home.product.detail.ProductDetailViewModel
import id.grafindo.gsb.ui.home.product.list.ProductListViewModel
import id.grafindo.gsb.ui.library.LibraryViewModel
import id.grafindo.gsb.ui.library.detail.BookDetailViewModel
import id.grafindo.gsb.ui.library.detail.settings.BookDetailSettingsViewModel
import id.grafindo.gsb.ui.pdf.PdfReaderViewModel
import id.grafindo.gsb.ui.pdf.content.pager.PdfContentBookmarkViewModel
import id.grafindo.gsb.ui.profile.ProfileViewModel
import id.grafindo.gsb.ui.profile.customercare.CustomerCareViewModel
import id.grafindo.gsb.ui.profile.updatepassword.UpdatePasswordViewModel
import id.grafindo.gsb.util.API_BASE_URL
import id.grafindo.gsb.util.WP_BASE_URL
import id.grafindo.gsb.util.coil.ByteArrayFetcher
import id.grafindo.gsb.util.network.NetworkStatusTracker
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asExecutor
import kotlinx.serialization.json.Json
import okhttp3.Cache
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.koin.android.ext.koin.androidApplication
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module
import retrofit2.Retrofit
import timber.log.Timber
import java.util.concurrent.TimeUnit

val catalogModule = module {
    // repository
    single<CatalogRepository> { CatalogRepositoryImpl(get()) }

    // useCase
    factory<HomeUseCase> { HomeInteractor(get()) }
    factory<CatalogUseCase> { CatalogInteractor(get()) }
    factory<ProductUseCase> { ProductInteractor(get()) }

    // viewModel
    viewModel { HomeViewModel(get(), get(), get()) }
    viewModel { (parentId: Int) ->
        CatalogDetailViewModel(parentId, get())
    }
    viewModel { ProductListViewModel(get()) }
    viewModel { (productId: Int) ->
        ProductDetailViewModel(productId, get())
    }
}

val bookModule = module {
    // repository
    single<BookRepository> { BookRepositoryImpl(androidContext(), get()) }
    single<ExamRepository> { ExamRepositoryImpl(androidContext(), get(), get()) }

    // useCase
    factory<LibraryUseCase> { LibraryInteractor(get()) }
    factory<ExamUseCase> { ExamInteractor(get()) }

    // viewModel
    viewModel { LibraryViewModel(get(), get(), get()) }
    viewModel { (token: String, bookId: Long) ->
        BookDetailViewModel(token, bookId, get(), get(), get())
    }
    viewModel { (bookId: Long) ->
        BookDetailSettingsViewModel(bookId, get())
    }
    viewModel { (bookId: Long) ->
        PdfReaderViewModel(bookId, get(), get())
    }
    viewModel { (bookId: Long) ->
        PdfContentBookmarkViewModel(bookId, get())
    }
    viewModel { (bookId: Long) ->
        ExamListViewModel(bookId, get(), get())
    }
    viewModel { (bookId: Long, examId: Long) ->
        ExamPrefaceViewModel(bookId, examId, get())
    }
    viewModel { (bookId: Long, examId: Long) ->
        ExamViewModel(bookId, examId, get())
    }
    viewModel { (bookId: Long, examId: Long) ->
        ExamSubmitViewModel(bookId, examId, get(), get(), get())
    }
}

val authModule = module {
    // repository
    single<AuthRepository> { AuthRepositoryImpl(get(), get()) }

    // useCase
    factory<LoginUseCase> { LoginInteractor(get()) }
    factory<ProfileUseCase> { ProfileInteractor(get(), get()) }
    factory<ActivationUseCase> { ActivationInteractor(get()) }
    factory<ForgotPasswordUseCase> { ForgotPasswordInteractor(get()) }
    factory<UpdatePasswordUseCase> { UpdatePasswordInteractor(get()) }
    factory<AgreementUseCase> { AgreementInteractor(get()) }
    factory<CustomerCareUseCase> { CustomerCareInteractor(get()) }

    // viewModel
    viewModel { LoginViewModel(get()) }
    viewModel { ProfileViewModel(get(), get()) }
    viewModel { ActivationViewModel(get(), get()) }
    viewModel { ForgotPasswordViewModel(get()) }
    viewModel { UpdatePasswordViewModel(get(), get()) }
    viewModel { AgreementViewModel(get()) }
    viewModel { CustomerCareViewModel(get(), get()) }
}

val databaseModule = module {
    fun provideAppDatabase(context: Context, userId: Long): AppDatabase =
        Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            if (userId != 0L) "$userId-gsb.db" else "gsb.db"
        )
            .addMigrations(MIGRATION_1_2)
            .addMigrations(MIGRATION_1_3)
            .addMigrations(MIGRATION_3_4)
            .setQueryExecutor(Dispatchers.IO.asExecutor())
            .setTransactionExecutor(Dispatchers.IO.asExecutor())
            .build()

    single { provideAppDatabase(androidContext(), get<AppPreference>().getUserIdSiswa()) }
}

private val json = Json {
    ignoreUnknownKeys = true
    isLenient = true
}

@Suppress("MagicNumber")
val networkModule = module {
    fun provideNetworkCache(application: Application): Cache {
        val cacheSize: Long = 10L * 1024L * 1024L
        return Cache(application.cacheDir, cacheSize)
    }

    fun provideHttpClient(cache: Cache): OkHttpClient {
        val builder = OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            // Meningkatkan ukuran buffer untuk response besar
            .retryOnConnectionFailure(true)
            .followRedirects(true)
            .followSslRedirects(true)

        if (BuildConfig.DEBUG) {
            val logger = HttpLoggingInterceptor { message ->
                // Skip logging large response bodies
                if (message.length > 1000) {
                    Timber.d("API: [Response too large to log]")
                } else if (message.startsWith('{') || message.startsWith('[')) {
                    Timber.d("API: ${message.take(500)}...")
                } else {
                    Timber.d("API: $message")
                }
            }.apply {
                // Only log headers and basic info, not the body
                level = HttpLoggingInterceptor.Level.HEADERS
            }
            builder.addNetworkInterceptor(logger)
        }

        // Menambahkan interceptor untuk menangani response besar
        builder.addNetworkInterceptor { chain ->
            val request = chain.request()
            val response = chain.proceed(request)
            
            // Skip caching untuk response yang sangat besar
            if (response.body?.contentLength() ?: 0 > 10 * 1024 * 1024) { // 10MB
                response.newBuilder()
                    .header("Cache-Control", "no-store, no-cache, must-revalidate")
                    .build()
            } else {
                response
            }
        }

        return builder
            .cache(cache)
            .build()
    }

    single { provideNetworkCache(androidApplication()) }
    single { provideHttpClient(get()) }

    @OptIn(ExperimentalStdlibApi::class)
    fun provideApiService(client: OkHttpClient): ApiService {
        val contentType = "application/json".toMediaType()
        
        // Create a custom JSON instance with more memory-efficient settings
        val jsonConfig = Json {
            ignoreUnknownKeys = true
            isLenient = true
            explicitNulls = false
        }
        
        // Use the custom JSON instance for the converter
        val jsonConverterFactory = jsonConfig.asConverterFactory(contentType)

        val retrofit = Retrofit.Builder()
            .baseUrl(API_BASE_URL)
            .client(client.newBuilder()
                .addNetworkInterceptor { chain ->
                    val request = chain.request()
                    // For large responses, we'll handle the body in the worker
                    if (request.header("Skip-Body-Processing") == "true") {
                        val newRequest = request.newBuilder()
                            .removeHeader("Skip-Body-Processing")
                            .build()
                        chain.proceed(newRequest)
                    } else {
                        chain.proceed(request)
                    }
                }
                .build())
            .addConverterFactory(jsonConverterFactory)
            .build()

        return retrofit.create(ApiService::class.java)
    }

    @OptIn(ExperimentalStdlibApi::class)
    fun provideWPApiService(client: OkHttpClient): WPApiService {
        val contentType = "application/json".toMediaType()
        
        // Use the same JSON config as provideApiService
        val jsonConfig = Json {
            ignoreUnknownKeys = true
            isLenient = true
            explicitNulls = false
        }
        
        val jsonConverterFactory = jsonConfig.asConverterFactory(contentType)

        val retrofit = Retrofit.Builder()
            .baseUrl(WP_BASE_URL)
            .client(client.newBuilder()
                .addNetworkInterceptor { chain ->
                    val request = chain.request()
                    // For large responses, we'll handle the body in the worker
                    if (request.header("Skip-Body-Processing") == "true") {
                        val newRequest = request.newBuilder()
                            .removeHeader("Skip-Body-Processing")
                            .build()
                        chain.proceed(newRequest)
                    } else {
                        chain.proceed(request)
                    }
                }
                .build())
            .addConverterFactory(jsonConverterFactory)
            .build()

        return retrofit.create(WPApiService::class.java)
    }

    single { provideApiService(get()) }
    single { provideWPApiService(get()) }
}

val libModule = module {
    // coil
    fun provideCoilLoader(
        app: Application,
        client: OkHttpClient,
    ) = ImageLoader.Builder(app)
        .okHttpClient(client)
        .crossfade(true)
        .componentRegistry {
            add(ByteArrayFetcher())
        }
        .build()

    single { provideCoilLoader(androidApplication(), get()) }

    // pref
    fun provideAppPreference(context: Context): AppPreference =
        AppPreference(context)

    single { provideAppPreference(androidContext()) }

    // network status
    fun provideNetworkStatusTracker(context: Context): NetworkStatusTracker =
        NetworkStatusTracker(context)

    single { provideNetworkStatusTracker(androidContext()) }
}
